on run {input, parameters}
	
	-- Définir le dossier de destination principal
	set photosFolder to (path to pictures folder as string) & "Photos Organisées:"
	
	-- C<PERSON><PERSON> le dossier principal s'il n'existe pas
	tell application "Finder"
		if not (exists folder photosFolder) then
			make new folder at (path to pictures folder) with properties {name:"Photos Organisées"}
		end if
	end tell
	
	-- Extensions d'images supportées
	set imageExtensions to {"jpg", "jpeg", "png", "tiff", "tif", "heic", "raw", "cr2", "nef", "arw", "dng"}
	
	-- Parcourir chaque élément sélectionné
	repeat with currentItem in input
		set currentPath to POSIX path of currentItem
		
		-- Vérifier si c'est un dossier
		tell application "System Events"
			if (kind of disk item currentPath) is "Folder" then
				-- Récupérer tous les fichiers du dossier (récursivement)
				set allFiles to my getAllFiles(currentPath)
				
				-- Traiter chaque fichier
				repeat with aFile in allFiles
					my processPhotoFile(aFile, photosFolder)
				end repeat
			else
				-- Traiter un seul fichier s'il est sélectionné
				my processPhotoFile(currentPath, photosFolder)
			end if
		end tell
	end repeat
	
	-- Afficher le résultat
	tell application "Finder"
		open folder photosFolder
	end tell
	
	display notification "Organisation des photos terminée !" with title "Automator Photos"
	
	return input
end run

-- Fonction pour obtenir tous les fichiers d'un dossier (récursivement)
on getAllFiles(folderPath)
	set fileList to {}
	
	tell application "System Events"
		set folderItems to every disk item of folder folderPath
		
		repeat with anItem in folderItems
			set itemPath to POSIX path of anItem
			
			if (kind of anItem) is "Folder" then
				-- Récursion pour les sous-dossiers
				set subFiles to my getAllFiles(itemPath)
				set fileList to fileList & subFiles
			else
				-- Ajouter le fichier à la liste
				set fileList to fileList & {itemPath}
			end if
		end repeat
	end tell
	
	return fileList
end getAllFiles

-- Fonction pour traiter un fichier photo
on processPhotoFile(filePath, photosFolder)
	-- Extensions d'images supportées
	set imageExtensions to {"jpg", "jpeg", "png", "tiff", "tif", "heic", "raw", "cr2", "nef", "arw", "dng"}
	
	-- Extraire l'extension du fichier
	set fileName to my getFileName(filePath)
	set fileExtension to my getFileExtension(fileName)
	
	-- Vérifier si c'est un fichier image
	if fileExtension is in imageExtensions then
		-- Obtenir la date de prise de vue
		set photoDate to my getPhotoDate(filePath)
		
		if photoDate is not "" then
			-- Créer le nom du dossier basé sur la date (Format: YYYY-MM-DD)
			set dateFolder to my formatDateForFolder(photoDate)
			set destinationFolder to photosFolder & dateFolder & ":"
			
			-- Créer le dossier de destination s'il n'existe pas
			tell application "Finder"
				if not (exists folder destinationFolder) then
					make new folder at folder photosFolder with properties {name:dateFolder}
				end if
			end tell
			
			-- Copier le fichier
			try
				tell application "Finder"
					set sourceFile to (POSIX file filePath) as alias
					duplicate sourceFile to folder destinationFolder with replacing
				end tell
			on error errMsg
				log "Erreur lors de la copie de " & fileName & ": " & errMsg
			end try
		else
			-- Si pas de date EXIF, utiliser la date de modification du fichier
			set modDate to my getFileModificationDate(filePath)
			set dateFolder to my formatDateForFolder(modDate)
			set destinationFolder to photosFolder & dateFolder & ":"
			
			-- Créer le dossier de destination s'il n'existe pas
			tell application "Finder"
				if not (exists folder destinationFolder) then
					make new folder at folder photosFolder with properties {name:dateFolder}
				end if
			end tell
			
			-- Copier le fichier
			try
				tell application "Finder"
					set sourceFile to (POSIX file filePath) as alias
					duplicate sourceFile to folder destinationFolder with replacing
				end tell
			on error errMsg
				log "Erreur lors de la copie de " & fileName & ": " & errMsg
			end try
		end if
	end if
end processPhotoFile

-- Fonction pour extraire la date EXIF d'une photo
on getPhotoDate(filePath)
	try
		set exifData to (do shell script "mdls -name kMDItemContentCreationDate " & quoted form of filePath)
		
		if exifData contains "null" then
			return ""
		else
			-- Extraire la date de la chaîne retournée
			set dateStart to (offset of "\"" in exifData) + 1
			set dateEnd to (offset of "\"" in (text dateStart to -1 of exifData)) + dateStart - 2
			set dateString to text dateStart thru dateEnd of exifData
			return dateString
		end if
	on error
		return ""
	end try
end getPhotoDate

-- Fonction pour obtenir la date de modification du fichier
on getFileModificationDate(filePath)
	try
		tell application "System Events"
			set modDate to modification date of (disk item filePath)
			return (modDate as string)
		end tell
	on error
		return (current date) as string
	end try
end getFileModificationDate

-- Fonction pour formater la date pour le nom du dossier
on formatDateForFolder(dateString)
	try
		set dateObj to date dateString
		set yearNum to year of dateObj
		set monthNum to month of dateObj as integer
		set dayNum to day of dateObj as integer
		
		-- Formater le mois avec un zéro si nécessaire
		if monthNum < 10 then
			set monthStr to "0" & monthNum
		else
			set monthStr to monthNum as string
		end if
		
		-- Formater le jour avec un zéro si nécessaire
		if dayNum < 10 then
			set dayStr to "0" & dayNum
		else
			set dayStr to dayNum as string
		end if
		
		return (yearNum as string) & "-" & monthStr & "-" & dayStr
	on error
		return "Date_Inconnue"
	end try
end formatDateForFolder

-- Fonction pour extraire le nom du fichier
on getFileName(filePath)
	set AppleScript's text item delimiters to "/"
	set fileName to last text item of filePath
	set AppleScript's text item delimiters to ""
	return fileName
end getFileName

-- Fonction pour extraire l'extension du fichier
on getFileExtension(fileName)
	set AppleScript's text item delimiters to "."
	set fileExtension to last text item of fileName
	set AppleScript's text item delimiters to ""
	return (fileExtension as string)
end getFileExtension