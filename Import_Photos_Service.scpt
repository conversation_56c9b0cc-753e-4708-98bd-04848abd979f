on run {input, parameters}
	
	-- Définir le dossier de destination principal
	set importFolder to (path to pictures folder as string) & "import:"
	
	-- <PERSON><PERSON>er le dossier principal s'il n'existe pas
	tell application "Finder"
		if not (exists folder importFolder) then
			make new folder at (path to pictures folder) with properties {name:"import"}
		end if
	end tell
	
	-- Extensions d'images supportées (selon vos spécifications)
	set imageExtensions to {"jpg", "raf", "dng"}
	
	-- Compteur pour les fichiers traités
	set filesProcessed to 0
	
	-- Parcourir chaque élément sélectionné
	repeat with currentItem in input
		set currentPath to POSIX path of currentItem
		
		-- Vérifier si c'est un dossier
		tell application "System Events"
			if (kind of disk item currentPath) is "Folder" then
				-- Récupérer tous les fichiers du dossier (récursivement)
				set allFiles to my getAllFiles(currentPath)
				
				-- Traiter chaque fichier
				repeat with aFile in allFiles
					set result to my processPhotoFile(aFile, importFolder, imageExtensions)
					if result then set filesProcessed to filesProcessed + 1
				end repeat
			else
				-- Traiter un seul fichier s'il est sélectionné
				set result to my processPhotoFile(currentPath, importFolder, imageExtensions)
				if result then set filesProcessed to filesProcessed + 1
			end if
		end tell
	end repeat
	
	-- Afficher le résultat
	if filesProcessed > 0 then
		tell application "Finder"
			open folder importFolder
		end tell
		display notification (filesProcessed as string) & " fichier(s) importé(s) avec succès !" with title "Import Photos"
	else
		display notification "Aucun fichier image (.jpg, .raf, .dng) trouvé dans la sélection." with title "Import Photos"
	end if
	
	return input
end run

-- Fonction pour obtenir tous les fichiers d'un dossier (récursivement)
on getAllFiles(folderPath)
	set fileList to {}
	
	try
		tell application "System Events"
			set folderItems to every disk item of folder folderPath
			
			repeat with anItem in folderItems
				set itemPath to POSIX path of anItem
				
				if (kind of anItem) is "Folder" then
					-- Récursion pour les sous-dossiers
					set subFiles to my getAllFiles(itemPath)
					set fileList to fileList & subFiles
				else
					-- Ajouter le fichier à la liste
					set fileList to fileList & {itemPath}
				end if
			end repeat
		end tell
	on error errMsg
		log "Erreur lors de l'exploration du dossier " & folderPath & ": " & errMsg
	end try
	
	return fileList
end getAllFiles

-- Fonction pour traiter un fichier photo
on processPhotoFile(filePath, importFolder, imageExtensions)
	
	-- Extraire l'extension du fichier
	set fileName to my getFileName(filePath)
	set fileExtension to my getFileExtension(fileName)
	
	-- Vérifier si c'est un fichier image supporté
	if fileExtension is in imageExtensions then
		-- Obtenir la date de prise de vue
		set photoDate to my getPhotoDate(filePath)
		
		if photoDate is not "" then
			-- Créer le nom du dossier basé sur la date (Format: YYYY-MM-DD)
			set dateFolder to my formatDateForFolder(photoDate)
		else
			-- Si pas de date EXIF, utiliser la date de modification du fichier
			set modDate to my getFileModificationDate(filePath)
			set dateFolder to my formatDateForFolder(modDate)
		end if
		
		set destinationFolder to importFolder & dateFolder & ":"
		
		-- Créer le dossier de destination s'il n'existe pas
		tell application "Finder"
			if not (exists folder destinationFolder) then
				make new folder at folder importFolder with properties {name:dateFolder}
			end if
		end tell
		
		-- Copier le fichier
		try
			tell application "Finder"
				set sourceFile to (POSIX file filePath) as alias
				duplicate sourceFile to folder destinationFolder with replacing
			end tell
			return true
		on error errMsg
			log "Erreur lors de la copie de " & fileName & ": " & errMsg
			return false
		end try
	else
		return false
	end if
end processPhotoFile

-- Fonction pour extraire la date EXIF d'une photo
on getPhotoDate(filePath)
	try
		-- Utiliser mdls pour obtenir la date de création du contenu (date de prise de vue)
		set exifData to (do shell script "mdls -name kMDItemContentCreationDate " & quoted form of filePath)

		if exifData contains "null" or exifData contains "(null)" then
			-- Essayer avec la date de création EXIF
			set exifData to (do shell script "mdls -name kMDItemDateTimeOriginal " & quoted form of filePath)
			if exifData contains "null" or exifData contains "(null)" then
				return ""
			end if
		end if

		-- Extraire la date de la chaîne retournée
		set firstQuote to offset of "\"" in exifData
		if firstQuote > 0 then
			set remainingText to text (firstQuote + 1) to -1 of exifData
			set secondQuote to offset of "\"" in remainingText
			if secondQuote > 0 then
				set dateString to text 1 thru (secondQuote - 1) of remainingText
				return dateString
			end if
		end if

		return ""

	on error
		return ""
	end try
end getPhotoDate

-- Fonction pour obtenir la date de modification du fichier
on getFileModificationDate(filePath)
	try
		tell application "System Events"
			set modDate to modification date of (disk item filePath)
			return (modDate as string)
		end tell
	on error
		return (current date) as string
	end try
end getFileModificationDate

-- Fonction pour formater la date pour le nom du dossier (YYYY-MM-DD)
on formatDateForFolder(dateString)
	try
		set dateObj to date dateString
		set yearNum to year of dateObj
		set monthNum to month of dateObj as integer
		set dayNum to day of dateObj as integer
		
		-- Formater le mois avec un zéro si nécessaire
		if monthNum < 10 then
			set monthStr to "0" & monthNum
		else
			set monthStr to monthNum as string
		end if
		
		-- Formater le jour avec un zéro si nécessaire
		if dayNum < 10 then
			set dayStr to "0" & dayNum
		else
			set dayStr to dayNum as string
		end if
		
		return (yearNum as string) & "-" & monthStr & "-" & dayStr
	on error
		-- En cas d'erreur, utiliser la date actuelle
		set currentDate to current date
		set yearNum to year of currentDate
		set monthNum to month of currentDate as integer
		set dayNum to day of currentDate as integer
		
		if monthNum < 10 then
			set monthStr to "0" & monthNum
		else
			set monthStr to monthNum as string
		end if
		
		if dayNum < 10 then
			set dayStr to "0" & dayNum
		else
			set dayStr to dayNum as string
		end if
		
		return (yearNum as string) & "-" & monthStr & "-" & dayStr
	end try
end formatDateForFolder

-- Fonction pour extraire le nom du fichier
on getFileName(filePath)
	set AppleScript's text item delimiters to "/"
	set fileName to last text item of filePath
	set AppleScript's text item delimiters to ""
	return fileName
end getFileName

-- Fonction pour extraire l'extension du fichier (en minuscules)
on getFileExtension(fileName)
	set AppleScript's text item delimiters to "."
	set fileExtension to last text item of fileName
	set AppleScript's text item delimiters to ""
	-- Convertir en minuscules pour la comparaison
	return my toLowercase(fileExtension as string)
end getFileExtension

-- Fonction pour convertir en minuscules
on toLowercase(str)
	set upperChars to "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	set lowerChars to "abcdefghijklmnopqrstuvwxyz"
	set newStr to ""
	
	repeat with i from 1 to length of str
		set currentChar to character i of str
		set charIndex to offset of currentChar in upperChars
		if charIndex > 0 then
			set newStr to newStr & character charIndex of lowerChars
		else
			set newStr to newStr & currentChar
		end if
	end repeat
	
	return newStr
end toLowercase
