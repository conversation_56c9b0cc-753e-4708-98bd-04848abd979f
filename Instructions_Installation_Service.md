# Installation du Service d'Import de Photos

Ce guide vous explique comment installer l'AppleScript comme service macOS pour organiser automatiquement vos photos.

## Fonctionnalités du Script

- **Extensions supportées** : .jpg, .raf, .dng (selon vos spécifications)
- **Exploration récursive** : Pa<PERSON>ourt tous les sous-dossiers du dossier sélectionné
- **Destination** : ~/Pictures/import/
- **Organisation** : Classe les photos dans des dossiers au format YYYY-MM-DD basé sur la date de prise de vue
- **Fallback intelligent** : Utilise la date de modification si pas de données EXIF

## Installation du Service

### Méthode 1 : Via Automator (Recommandée)

1. **Ouvrir Automator**
   - Lancez l'application Automator (Applications > Automator)
   - Choisissez "Service" comme nouveau document

2. **Configurer le Service**
   - Dans "Le service reçoit" : sélectionnez "fichiers ou dossiers"
   - Dans "dans" : sélectionnez "Finder"
   - Cochez "Sortie remplace l'entrée sélectionnée" si vous le souhaitez

3. **Ajouter l'Action AppleScript**
   - Dans la bibliothèque d'actions, cherchez "Exécuter un script AppleScript"
   - Faites glisser cette action dans la zone de workflow
   - Supprimez le code par défaut et copiez-collez le contenu du fichier `Import_Photos_Service.scpt`

4. **Sauvegarder le Service**
   - Fichier > Enregistrer
   - Nommez le service (ex: "Importer Photos par Date")
   - Le service sera automatiquement sauvé dans ~/Library/Services/

### Méthode 2 : Installation Manuelle

1. **Créer le fichier de service**
   ```bash
   mkdir -p ~/Library/Services
   ```

2. **Copier le script dans Automator** et suivre les étapes de la Méthode 1

## Utilisation du Service

1. **Dans le Finder** :
   - Sélectionnez un ou plusieurs dossiers contenant des photos
   - Clic droit sur la sélection
   - Dans le menu contextuel, allez dans "Services"
   - Cliquez sur "Importer Photos par Date" (ou le nom que vous avez donné)

2. **Le script va** :
   - Explorer récursivement tous les dossiers sélectionnés
   - Identifier les fichiers .jpg, .raf et .dng
   - Lire les métadonnées EXIF pour obtenir la date de prise de vue
   - Créer des dossiers au format YYYY-MM-DD dans ~/Pictures/import/
   - Copier les photos dans les dossiers appropriés
   - Afficher une notification avec le nombre de fichiers traités

## Structure des Dossiers de Destination

```
~/Pictures/import/
├── 2024-01-15/
│   ├── IMG_001.jpg
│   ├── DSC_002.raf
│   └── photo_003.dng
├── 2024-01-16/
│   ├── IMG_004.jpg
│   └── DSC_005.raf
└── 2024-02-01/
    ├── photo_006.dng
    └── IMG_007.jpg
```

## Fonctionnalités Avancées

- **Gestion des doublons** : Les fichiers existants sont remplacés
- **Gestion d'erreurs** : Les erreurs sont loggées dans la Console
- **Fallback de date** : Si pas de données EXIF, utilise la date de modification
- **Notifications** : Affiche le résultat de l'opération
- **Ouverture automatique** : Ouvre le dossier d'import après traitement

## Dépannage

### Le service n'apparaît pas dans le menu contextuel
- Vérifiez que le service est bien sauvé dans ~/Library/Services/
- Redémarrez le Finder : Option + Clic droit sur l'icône Finder dans le Dock > Relancer

### Erreurs de permissions
- Assurez-vous qu'Automator a les permissions d'accès aux fichiers
- Allez dans Préférences Système > Sécurité et confidentialité > Confidentialité > Accès complet au disque
- Ajoutez Automator si nécessaire

### Photos non organisées correctement
- Vérifiez que les fichiers ont bien les extensions .jpg, .raf ou .dng
- Consultez la Console (Applications > Utilitaires > Console) pour voir les messages d'erreur

## Personnalisation

Pour modifier le comportement du script :

1. **Changer les extensions supportées** : Modifiez la ligne `set imageExtensions to {"jpg", "raf", "dng"}`
2. **Changer le dossier de destination** : Modifiez la ligne avec `"import:"`
3. **Modifier le format de date** : Ajustez la fonction `formatDateForFolder`

## Notes Importantes

- Le script copie les fichiers (ne les déplace pas)
- Les fichiers sources restent intacts dans leur emplacement original
- Le script respecte la casse des extensions mais les compare en minuscules
- Compatible avec macOS 10.14 et versions ultérieures
